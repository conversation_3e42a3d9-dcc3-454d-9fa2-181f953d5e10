# 萤火虫ADX程序化接入文档

#### 萤火虫ADX程序化接入文档

## 更新日志

| **版本** | **更新日期** | **更新说明** |
| --- | --- | --- |
| 1.0 | 2024.11.18 | 初版 |
| 1.1 | 2024.12.16 | 展现监测链接支持竞胜价格加密宏替换 |
| 1.2 | 2024.12.27 | 新增广告创意宽高比：2:3、3:2、1:2、2:1 |
| 1.3 | 2025.1.10 | 新增pkglist |
| 1.4 | 2025.3.10 | 新增cpc报价方式 |

## 文档简介

本接口文档介绍了广告方如何基于API的合作方式获取萤火虫优质流量，下文是广告接口的详细描述。

## 接入流程

*   达成商务合作，萤火虫向广告方提供接口协议文档
    
*   广告方按照萤火虫接口协议进行开发对接
    
*   DSP完成开发自测，申请联调时间，双方进行联调测试
    
*   联调测试通过，进行小流量验数（包括展现、点击、收入等金额）
    
*   验数通过，开始正式跑量
    

## 4. 接口描述

### 4.1 基础协议

| **Method** | Post |  |
| --- | --- | --- |
| **编码** | UTF-8 |  |
| **content-type** | application/json | JSON请求 |

### 4.2 请求参数

##### 1）请求-整体参数

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| reqId | string | Optional | 自定义请求id，保证唯一性 |
| ip | string | Required | ip 地址 |
| ipv6 | string | Optional | ipv6 |
| adSlots | AdSlot\[\] | Required | 广告位数组 |
| app | App | Optional | 应用信息 |
| device | Device | Required | 用户设备信息 |
| userLocation | UserLocation | Required | 位置信息 |
| tmax | uint32 | Optional | 请求等待时间 |
| reportPriceType | ReportPriceType | Optional | 本次请求的报价类型 |

ReportPriceType枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| UNKNOWN\_PRICING\_TYPE | 0 | 未知 |
| CPC | 1 | 广告以cpc报价，单位是元/每次点击 |
| CPM | 2 | 广告以cpm报价，单位是元/每千次曝光，默认是cpm报价方式 |

##### 2）AdSlot对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| appId | string | Required | 应用id |
| adSlotId | string | Required | 代码位id |
| bidFloor | uint32 | Optional | CPM底价，单位：分/每千次曝光 |
| cpcBidFloor | float | Optional | CPC底价，单位：元/每次点击 |
| adType | AdType | Optional | 广告类型 |
| actionType | ActionType\[\] | Optional | 请求广告的交互类型数组 |
| adInfo | AdInfo | Optional | 广告信息 |
| maxCount | uint32 | Optional | 期望单次请求返回多条广告个数最大值，返回广告小于等于该值，区间1-3 |
| materialType | MaterialType | Optional | 物料类型 |

AdInfo 对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| videoInfo | VideoInfo | Optional | 视频信息 |
| assets | Asset\[\] | Required | 广告创意样式诉求数组 |

AdType 枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| FEED | 1 | 信息流广告 |
| SPLASH | 2 | 开屏广告 |
| RVIDEO | 3 | 激励视频广告 |
| INSERT | 4 | 插屏广告 |

ActionType 枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| LANDINGPAGE | 1 | 广告点击后，跳转落地页 |
| QUICK\_APP | 2 | 快应用 |
| DOWNLOAD | 3 | 应用下载 |
| DEEPLINK | 4 | 应用唤醒 |

MaterialType 枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| AdMaterialTypeImg | 1 | 图片 |
| AdMaterialTypeVideo | 2 | 视频 |
| AdMaterialTypeImgOrVideo | 3 | 图片或视频 |

##### 3）Asset对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| ratio | AspectRatio | Required | 广告创意宽高比 |

AspectRatio 枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| RATIO\_38X25 | 1 | 物料比例 38:25 |
| RATIO\_9X16 | 2 | 物料比例 9:16 |
| RATIO\_16X9 | 3 | 物料比例 16:9 |
| RATIO\_2X3 | 4 | 物料比例 2:3 |
| RATIO\_3X2 | 5 | 物料比例 3:2 |
| RATIO\_2X1 | 6 | 物料比例 2:1 |
| RATIO\_1X2 | 7 | 物料比例 1:2 |

##### 4）App对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| bundle | string | Optional | 应用包名 |
| version | string | Optional | 应用版本号 |
| appType | AppType | Required | 应用类型 |
| name | string | Optional | 应用名称 |

AppType 枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| QUICK\_APP | 1 | 快应用 |
| APP | 2 | 普通app |

##### 5）Device对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| ua | string | Optional | User Agent |
| carrier | Carrier | Optional | 运营商 |
| networkType | NetworkType | Optional | 网络类型 |
| deviceType | DeviceType | Optional | 操作系统类型 |
| os | OS | Optional | 操作系统类型 |
| osv | string | Optional | 操作系统版本 |
| make | string | Optional | 设备厂商名称 |
| model | string | Optional | 设备型号，中文需要utf-8编码，样例：MX5 |
| brand | string | Optional | 设备品牌 |
| screenWidth | int32 | Optional | 屏幕宽度 |
| screenHeight | int32 | Optional | 屏幕高度 |
| uid | Uid | Required | 移动设备序列号标识字段，至少存在一种 |
| pkgList | string\[\] | Optional | 已安装的APP信息的对应编码，编码详见附录PkgList枚举 |

Carrier 枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| Unknown | 0 | 未知运营商 |
| Mobile | 1 | 中国移动 |
| Telecom | 2 | 中国电信 |
| Unicom | 3 | 中国联通 |

NetworkType枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| UNKNOWN\_NETWORK | 0 | 未知网络 |
| WIFI | 1 | wifi |
| MOBILE\_2G | 2 | 2G |
| MOBILE\_3G | 3 | 3G |
| MOBILE\_4G | 4 | 4G |
| MOBILE\_5G | 5 | 5G |

DeviceType 枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| UNKNOWN\_DEVICE | 0 | 未知设备 |
| PHONE | 1 | 手机 |
| PAD | 2 | 平板 |

OS 枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| UNKNOWN\_OS | 0 | 未知系统 |
| IOS | 1 | IOS |
| ANDROID | 2 | ANDROID |
| WINDOWS | 3 | window |
| HARMONY | 4 | 鸿蒙系统 |

##### 6）Uid对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| dpid | string | Optional | 明文Android Id |
| dpidMd5 | string | Optional | Android Id Md5 |
| oaid | string | Required | oaid |
| oaidMd5 | string | Optional | oaid Md5 |

##### 7）UserLocation对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| lat | float | Optional | 维度 |
| lon | float | Optional | 经度 |
| country | string | Optional | 国家代码遵循ISO-3166-1-alpha-2 |
| province | string | Optional | 省份 |
| city | string | Optional | 城市 |
| type | string | Optional | 坐标系类型 |
| provinceCode | string | Optional | 省份编码 |
| cityCode | string | Optional | 城市编码 |

##### 8）VideoInfo对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| duration | uint32 | Optional | 视频最大时长 |

##### 请求示例

```plaintext
{
	"reqId": "f_b713a7d37c616d6148eb4468912",
	"apiVersion": "v1",
	"ip": "************",
	"ipv6": "",
	"adSlots": [{
		"appId": "1309260435081859072",
		"adSlotId": "1309261134989561856",
		"bidFloor": 0,
		"adType": 1,
		"actionType": [1, 2, 3, 4],
		"adInfo": {
			"videoInfo": {
				"duration": 1
			},
			"assets": [{
				"ratio": 1
			}, {
				"ratio": 2
			}, {
				"ratio": 3
			}]
		},
		"maxCount": 1,
		"materialType": 3,
		"cpcBidFloor": 0
	}],
	"app": {
		"bundle": "com.xx.yy",
		"version": "2.0.0",
		"appType": 2,
		"name": "1121应用",
		"appPublisherId": 0
	},
	"device": {
		"ua": "Mozilla/5.0 (Linux; Android 12; BTKR-W00 Build/HUAWEIBTKR-W00;)AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/18.0.1025 Mobile Safari/537.36",
		"carrier": 2,
		"networkType": 2,
		"deviceType": 2,
		"os": 2,
		"osv": "1234",
		"make": "HUAWEI",
		"model": "BTKR-W00",
		"brand": "BTKR-W00",
		"screenWidth": 200,
		"screenHeight": 1000,
		"uid": {
			"dpid": "",
			"dpidMd5": "",
			"oaid": "43ef6aa3a218044f123",
			"oaidMd5": "11112",
			"deviceId": "43ef6aa3a218044f123"
		},
		"pkgList": ["10001", "10005", "10065", "10058"]
	},
	"tmax": 799,
	"reportPriceType": 2
}
```

### 4.3 响应参数

##### 1）响应-整体参数

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| reqId | string | Optinal | 请求id，保证唯一性 |
| seatBid | SeatBid\[\] | Optional | 广告位数组，与Imp对应，仅支持单广告位 |
| nbr | int32 | Optional | 无广告原因（尽量与nbr表对应） |
| msg | string | Optional | 错误信息 |
| expirationTime | int64 | Optional | 广告过期的时间戳 |

##### 2）SeatBid对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| bid | Bid\[\] | Required | 广告数组 |
| seat | string | Required | 出价者名称 |
| adLogo | string | Optional | 萤火虫广告logo |

##### 3）Bid对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| id | string | Required | 唯一标识该bid |
| adSlotId | string | Required | 广告位id，双方约定即可 |
| adId | string | Required | 广告id；标识投放广告的id |
| advertiserId | string | Optional | 广告账号id |
| campaignId | string | Optional | 计划id |
| createId | string | Required | 创意id |
| adm | Adm | Optional | 广告创意素材 |
| showUrl | string\[\] | Required | 广告在端上展现时的展现监测url 数组 |
| clickUrl | string\[\] | Optional | 广告被点击时的点击监测url 数组 |
| price | uint64 | Optional | 本广告cpm出价，单位分/每千次曝光 |
| cpcPrice | float | Optional | 本广告cpc出价，单位元/每次点击 |
| nurl | string\[\] | Optional | 竞标成功回调URL；**如果不支持竞胜回调，不填充此字段即可** |
| lurl | string\[\] | Optional | 竞标失败回调URL |
| mons | TrackMonitor\[\] | Optional | 监控 |
| reportPriceType | ReportPriceType | Optional | 广告的报价类型，cpc或cpm<br>当报价类型为cpm时，广告出价为price；当报价类型为cpc时，广告出价为cpcPrice |

##### 4）Adm对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| adType | AdType | Required | 广告类型枚举 |
| actionType | actionType | Required | 广告的交互类型 |
| title | string | Optional | 广告标题 |
| subTitle | string | Optional | 副标题 |
| desc | string | Optional | 广告描述 |
| icon | string | Optional | 图标地址 |
| imgInfo | Image\[\] | Optional | 图片信息数组 |
| videoInfo | Video | Optional | 视频信息 |
| androidApp | AndroidApp | Optional | Android应用下载和应用唤醒类预算相关信息 |
| landingPage | LandingPage | Required | 广告点击落地页/下载地址，deeplink失败时备用地址 |
| deeplink | string | Optional | deeplink地址 |
| quickAppLink | string | Optional | 快应用app链接 |
| materialType | uint | Required | 返回物料类型 1图片 2视频 |
| ratio | int32 | Optional | 物料尺寸 |

##### 5）Image对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| url | string | Required | 图片地址 |
| width | uint32 | Required | 图片宽 |
| height | uint32 | Required | 图片高 |
| description | string | Optional | 图片描述 |

##### 6）Video对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| url | string | Required | 视频地址 |
| width | uint32 | Required | 视频宽 |
| height | uint32 | Required | 视频高 |
| duration | uint32 | Optional | 视频时长，单位s |
| size | uint32 | Optional | 视频大小，单位Byte |
| ratio | uint32 | Optional | 视频码率 |
| coverUrl | string | Optional | 视频封面地址 |
| coverWidth | uint32 | Optional | 封面宽度 |
| coverHeight | uint32 | Optional | 封面高度 |

##### 7）AndroidApp对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| appName | string | Optional | APP名称，应用下载类预算必填 |
| package | string | Required | 应用包名，应用唤醒和应用下载类预算必填 |
| packageSize | uint32 | Optional | 应用包大小 |
| appVersion | string | Optional | 版本号 |
| downloadUrl | string | Optional | 下载链接 |
| openUrl | string | Optional | 安装之后打开应用的URL |
| marketUrl | string | Optional | 应用的详情页链接URL |
| publisher | string | Optional | 开发者，应用下载类预算必填 |
| privacy | string | Optional | 隐私协议，应用下载类预算必填 |
| permission | string | Optional | 用户权限，应用下载类预算必填 |
| appIntroductionLink | string | Optional | APP产品功能介绍链接，应用下载类预算必填 |
| downloadMidPage | string | Optional | 下载中间页 |
| regNumber | string | Optional | 备案号，应用下载类预算必填 |

##### 8）LandingPage对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| url | string | Optional | deeplink 失败时备用地址 |
| lpOpenUrl | string | Optional | 落地页调起应用需要的URL |

##### 9）TrackMonitor对象

| **字段名称** | **类型** | **是否必填** | **说明** |
| --- | --- | --- | --- |
| event | TrackEvent | Optional | 视频行为监控事件 |
| url | string\[\] | Optional | 视频行为监控url数组 |

TrackEvent 枚举

| **枚举值** | **整数值** | **说明** |
| --- | --- | --- |
| VIDEO\_START | 0 | 视频开始播放 |
| VIDEO\_CLOSE | 1 | 视频播放终止 |
| VIDEO\_READY\_PLAY | 2 | 视频准备播放 |
| VIDEO\_CONTINUE\_PLAY | 3 | 视频继续播放 |
| VIDEO\_PAUSE | 4 | 视频播放中止 |
| VIDEO\_PLAY\_END | 5 | 视频播放完成 |
| VIDEO\_REPEATED\_PLAY | 6 | 视频重复播放 |
| SKIP | 7 | 视频跳过 |
| VIDEO\_PLAY\_FAIL | 8 | 视频播放失败 |
| VIDEO\_TURN\_ON\_OFF\_SOUND\_BUTTON | 9 | 打开关闭声音 |

##### 响应示例

```plaintext
{
	"seatBid": [{
		"bid": [{
			"adm": {
				"adType": 2,
				"actionType": 1,
				"title": "解压神器",
				"desc": "正版解压神器，你也赶紧来试试！",
				"icon": "https://ads/icon/6/00a71cecc4da3b83b9767b47c48b3a0f.png",
				"imgInfo": [{
					"url": "https://rv/19/63fe2500/045ac508c68419bd5dc0036db4073d0e.jpeg",
					"width": 720,
					"height": 1280
				}],
				"androidApp": {
					"package": "com.t.test"
				},
				"landingPage": {
					"url": "https://www.yhcplatform.com",
					"lpOpenUrl": "https://www.yhcplatform.com"

				},
				"deeplink": "taobao://m.taobao.com/tbopen/index.html",
				"materialType": 1,
				"ratio": 2
			},
			"showUrl": ["https://test.cn/track?c=CiQ1NTcwYzZkMi00YTYyLTExZjAtYjJjMC0wMDE2M2Uz"],
			"clickUrl": ["https://test.cn/track?c=CiQ1NTcwYzZkMi00YTYyLTExZjAtYjJjMC0wMDE2M2UzNGFl"],
			"price": 100,
			"nurl": ["https://test.cn/rtb/callback/standard/win?rid=5570c6d2-4a62-11f0-b2c0-00163e34aea"],
			"lurl": ["https://test.cn/rtb/callback/standard/lose?rid=5570c6"],
			"adIdStr": "16536"
		}],
		"seat": "firefly",
		"adLogo": ""
	}],
	"error_code": 200,
	"debug_string": "ok"
}
```

## 5. nbr无广告原因说明

| **nbr值** | **说明** |
| --- | --- |
| 0 | 成功 |
| \-1 | 未识别异常 |
| 100001 | 请求体错误，请求数据无法正常解析 |
| 100002 | 系统内部异常 |
| 100003 | 非法媒体ID |
| 100004 | 无效请求ID |
| 100005 | 缺少广告位信息 |
| 100006 | 缺少广告位 ID |
| 100007 | 缺少应用ID（appid）信息 |
| 100008 | 缺少广告位信息（thirdAdSlotId） |
| 100009 | 非法应用ID（appid） |
| 100010 | 非法广告位ID |
| 100011 | 缺少广告位类型 |
| 100012 | 非法的广告位类型 |
| 100013 | 广告位类型不匹配 |
| 100017 | 缺少广告诉求信息（assets） |
| 100018 | 无效的广告诉求信息（assets） |
| 100019 | 缺少设备信息 |
| 100020 | 无效的IP信息 |
| 100021 | 缺少有效ID信息 |
| 100022 | 配置错误 |
| 100023 | DSP内部超时 |
| 100024 | 应用包名错误 |
| 100025 | sha1 错误 |
| 100026 | ad\_type 错误 |
| 100027 | 接入方式错误 |
| 100028 | 物料比例不存在 |
| 100029 | 无效用户/验签没过 |
| 100030 | 无效sign |
| 100031 | 无效时间戳 |
| 200001 | 无合适广告，放弃竞价 |
| 200002 | 广告价格低于底价 |
| 200003 | 广告创意ID信息缺失 |
| 200004 | 曝光地址缺失 |
| 200005 | 点击上报地址缺失 |
| 200006 | 创意素材信息缺失 |
| 200007 | 广告模版信息缺失 |
| 200008 | 广告模版配置错误 |
| 200009 | 广告交互类型缺失 |
| 200010 | 应用唤醒信息错误 |
| 300001 | qps达到上限 |
| 300002 | 达到日请求上限 |
| 300011 | 响应为空/无广告填充 |

## 6.广告交互

| **广告交互** | **匹配预算** | **需要关联字段** |
| --- | --- | --- |
| 系统浏览器打开网页<br>（LANDINGPAGE） | 网页类预算 | h5 链接： LandinPage 对象 lpOpenUrl |
| 快应用<br>（ QUICK\_APP） | 快应用类预算 | hap 链接： Adm 对象里 quickAppLink<br>h5 链接： LandinPage 对象里 url （quickAppLink 调起失败，触发访问 url http降级链接） |
| 应用下载<br>（DOWNLOAD） | 下载类预算 | 应用直投链接：AndroidApp 对象里 marketUrl<br>apk 直接下载链接：AndroidApp 对象里 downloadUrl<br>h5 链接：LandinPage 对象里 url<br>deeplink：延迟唤醒链接 |
| 唤醒（DEEPLINK） | 唤醒类预算 | 唤醒链接：Adm 对象里 deeplink<br>h5 链接： LandinPage 对象里 url<br>注意：deepLink返回scheme协议头内容，deepLink 调起失败，退化到 LandingPage.url访问；包名信息需放填充在AndroidApp 对象里package |

## 7.宏替换

| **宏** | **类型** | **含义** |
| --- | --- | --- |
| ${\_\_AUCTION\_PRICE\_\_} | uint32 | 当报价类型为cpm时，替换此宏参回传竞胜价格，单位为分/每千次曝光；<br>竞胜、竞败、展现、点击监测链接支持宏替换，展现、点击监测中会对价格进行加密。 |
| ${\_\_CPC\_AUCTION\_PRICE\_\_} | float | 当报价类型为cpc时，替换此宏参回传竞胜价格，单位为元/每次点击；<br>展现、点击监测链接支持宏替换，展现、点击监测中会对价格进行加密; |

##### 注：价格解密说明（仅展现、点击监测链接会加密价格，竞胜、竞败回传不进行加密）

*   **加密：**先采用aes ecb/pkcs5padding，再采用base64-url-safe编码
    
*   解密：先采用base64-url-safe解码，再采用ecb/pkcs5padding解密
    
*   密钥：长度128位，即16个ASCII字符
    
*   示例：  
    明文：120  
    密钥：YEjp1EQlGI4m8zG8（此密钥仅作为示例参考，**正式密钥请联系萤火虫工作人员获取**）  
    密文： r2QTjdlbqk62\_vBZfjyuQg
    

## 8.PkgList枚举

| **应用名称** | **包名** | **编码** | **系统** |
| --- | --- | --- | --- |
| 淘宝 | com.taobao.taobao | 10001 | Android |
| 天猫 | com.tmall.wireless | 10002 | Android |
| 美团 | com.sankuai.meituan | 10003 | Android |
| 美团外卖 | com.sankuai.meituan.takeoutnew | 10004 | Android |
| 快手 | com.smile.gifmaker | 10005 | Android |
| 快手极速版 | com.kuaishou.nebula | 10006 | Android |
| 支付宝 | com.eg.android.AlipayGphone | 10007 | Android |
| 京东 | com.jingdong.app.mall | 10008 | Android |
| 京东金融 | com.jd.jrapp | 10009 | Android |
| 拼多多 | com.xunmeng.pinduoduo | 10010 | Android |
| 唯品会 | com.achievo.vipshop | 10011 | Android |
| 手百 | com.baidu.searchbox | 10012 | Android |
| 闲鱼 | com.taobao.idlefish | 10013 | Android |
| 滴滴 | com.sdu.didi.psnger | 10014 | Android |
| 携程 | ctrip.android.view | 10015 | Android |
| 网易严选 | com.netease.yanxuan | 10016 | Android |
| 链家 | com.homelink.android | 10017 | Android |
| 当当 | com.dangdang.buy2 | 10018 | Android |
| 饿了么 | me.ele | 10019 | Android |
| oppo商城 | [com.oppo.store](http://com.oppo.store) | 10020 | Android |
| vivo浏览器 | com.vivo.browser | 10021 | Android |
| 大众点评 | com.dianping.v1 | 10022 | Android |
| 得物： | com.shizhuang.duapp | 10023 | Android |
| 优酷： | com.youku.phone | 10024 | Android |
| 爱奇艺： | com.qiyi.video | 10025 | Android |
| 微博 | com.sina.weibo | 10026 | Android |
| 天眼查 | com.tianyancha.skyeye | 10027 | Android |
| 华为应用市场 | com.huawei.appmarket | 10028 | Android |
| 斗鱼 | air.tv.douyu.android | 10029 | Android |
| 肯德基 | com.yek.android.kfc.activitys | 10030 | Android |
| 腾讯视频 | com.tencent.qqlive | 10031 | Android |
| 抖音短视频 | com.ss.android.ugc.aweme | 10032 | Android |
| 贝壳找房 | com.lianjia.beike | 10033 | Android |
| 腾讯新闻 | com.tencent.news | 10034 | Android |
| 知乎 | com.zhihu.android | 10035 | Android |
| 58同城 | com.wuba | 10036 | Android |
| 哔哩哔哩 | tv.danmaku.bili | 10037 | Android |
| 今日头条极速版 | com.ss.android.article.lite | 10038 | Android |
| QQ音乐 | com.tencent.qqmusic | 10039 | Android |
| 安居客 | com.anjuke.android.app | 10040 | Android |
| 马蜂窝旅游 | com.mfw.roadbook | 10041 | Android |
| 去哪儿旅行 | com.Qunar | 10042 | Android |
| 同程旅行 | com.tongcheng.android | 10043 | Android |
| 汽车之家 | com.cubic.autohome | 10044 | Android |
| 掌上生活 | com.cmbchina.ccd.pluto.cmbActivity | 10045 | Android |
| 飞猪 | com.taobao.trip | 10046 | Android |
| 夸克浏览器 | com.quark.browser | 10047 | Android |
| 全民k歌 | com.tencent.karaoke | 10048 | Android |
| 酷狗音乐 | com.kugou.android | 10049 | Android |
| 网易云 | com.netease.cloudmusic | 10050 | Android |
| 腾讯地图 | com.tencent.map | 10051 | Android |
| QQ浏览器 | com.tencent.mtt | 10052 | Android |
| 高德地图 | com.autonavi.minimap | 10053 | Android |
| 招商银行 | cmb.pb | 10054 | Android |
| T3出行 | com.t3go.passenger | 10055 | Android |
| 哈罗 | com.jingyao.easybike | 10056 | Android |
| 小拉出行 | com.xiaolachuxing.user | 10057 | Android |
| uc浏览器 | com.UCMobile | 10058 | Android |
| 小红书 | com.xingin.xhs | 10059 | Android |
| 今日头条 | com.ss.android.article.news | 10060 | Android |
| 爱奇艺极速版 | com.qiyi.video.lite | 10061 | Android |
| 西瓜视频 | com.ss.android.article.video | 10062 | Android |
| 手淘特价版 | com.taobao.litetao | 10063 | Android |
| 点淘 | [com.taobao.live](http://com.taobao.live) | 10064 | Android |
| 抖音极速版 | com.ss.android.ugc.aweme.lite | 10065 | Android |
| 1688 | com.alibaba.wireless | 10066 | Android |
| 中国移动 | com.greenpoint.android.mc10086.activity | 10067 | Android |
| 百度极速版 | com.baidu.searchbox.lite | 10068 | Android |
| 翼支付 | com.chinatelecom.bestpayclient | 10069 | Android |
| 微信 | com.tencent.mm | 10070 | Android |
| 脉脉 | com.taou.maimai | 10071 | Android |
| 中信银行 | com.ecitic.bank.mobile | 10072 | Android |
| 芒果TV | com.hunantv.imgo.activity | 10073 | Android |
| UC浏览器极速版 | com.ucmobile.lite | 10074 | Android |
| 菜鸟 | com.cainiao.wireless | 10075 | Android |
| YY | com.duowan.mobile | 10076 | Android |
| 咪咕视频 | com.cmcc.cmvideo | 10077 | Android |
| 京东健康 | com.jd.jdhealth | 10084 | Android |