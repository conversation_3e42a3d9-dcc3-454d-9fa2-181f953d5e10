package cn.taken.ad.core.dto.web.oper.financial.merge;

import cn.taken.ad.core.dto.web.oper.system.task.SystemTaskExec;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Valid
public class FinancialMediaAdvTaskReq extends SystemTaskExec {

    @NotNull(message = "时间未选择")
    private String reportTime;
    @NotNull(message = "时间未选择")
    private String beginTime;
    @NotNull(message = "时间未选择")
    private String endTime;

    //预算id
    private Long [] advIds;
    //预算appId
    private Long [] advAppIds;
    //预算广告未Id
    private Long [] advTagIds;

    public String getReportTime() {
        return reportTime;
    }

    public void setReportTime(String reportTime) {
        this.reportTime = reportTime;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Long[] getAdvIds() {
        return advIds;
    }

    public void setAdvIds(Long[] advIds) {
        this.advIds = advIds;
    }

    public Long[] getAdvAppIds() {
        return advAppIds;
    }

    public void setAdvAppIds(Long[] advAppIds) {
        this.advAppIds = advAppIds;
    }

    public Long[] getAdvTagIds() {
        return advTagIds;
    }

    public void setAdvTagIds(Long[] advTagIds) {
        this.advTagIds = advTagIds;
    }
}
